---
- hosts:
    - beelink
  vars_files:
    - vault.yml
  roles:
    - role: betula
      tags:
        - betula
      vars:
        betula_directory_path: "/home/<USER>/betula/"
        betula_database_backup_path: "/mnt/sdb1/backups/links.betula"
        betula_database_path: "/home/<USER>/betula/db/links.betula"
    - role: ssh_port_forward
      tags:
        - ssh_port_forward
    - role: syncthing
      tags:
        - syncthing
    - role: transmission
      tags:
        - transmission
    - role: zyr
      vars:
        chrome_executable_path: "/snap/bin/chromium"
        chrome_user_data_path: "/data/zyr/kinopoisk/chrome-user-data"
        cookie_signing_key: "{{ vault_cookie_signing_key }}"
        deepseek_api_key: "{{ vault_deepseek_api_key }}"
        images_path: "/data/zyr/nginx/img/"
        kinanet_pdf_marks_directory_path: "/data/zyr/kinopoisk/kinanet"
        kinopoisk_stderr_path: "/data/zyr/kinopoisk/logs/stderr.log"
        kinopoisk_stdout_path: "/data/zyr/kinopoisk/logs/stdout.log"
        llm_stderr_path: "/data/zyr/llm/logs/stderr.log"
        llm_stdout_path: "/data/zyr/llm/logs/stdout.log"
        nginx_img: "/data/zyr/nginx/img/"
        nginx_logs: "/data/zyr/nginx/logs"
        nginx_static: "/data/zyr/nginx/www"
        port: 8001
        postgres_backup_path: "/mnt/sdb1/backups/zyr.sql"
        postgres_connection_string: "socket://nikityy:{{ vault_postgres_password }}@/var/run/postgresql?db=zyr"
        suggester_url: "http://127.0.0.1:8001"
        telegram_cache_directory_path: "/data/zyr/telegram/cache"
        tmdb_api_key: "{{ vault_tmdb_api_key }}"
        tmdb_stderr_path: "/data/zyr/tmdb/logs/stderr.log"
        tmdb_stdout_path: "/data/zyr/tmdb/logs/stdout.log"
        wikidata_stderr_path: "/data/zyr/wikidata/logs/stderr.log"
        wikidata_stdout_path: "/data/zyr/wikidata/logs/stdout.log"
        wikimedia_access_token: "{{ vault_wikimedia_access_token }}"
        wikimedia_stderr_path: "/data/zyr/wikimedia/logs/stderr.log"
        wikimedia_stdout_path: "/data/zyr/wikimedia/logs/stdout.log"
- hosts:
    - raspberry
  vars_files:
    - vault.yml
  roles:
    - role: ingress
    - role: aleksivanchenko
      vars:
        app_json_path: /home/<USER>/aleksivanchenko/fashione.json
        dest_path: /data/www/
        dir: /home/<USER>/aleksivanchenko
        domain: fashione.by
        source_path: /data/shared/fashione.by
      tags:
        - aleksivanchenko
        - fashione.by
    - role: aleksivanchenko
      vars:
        app_json_path: /home/<USER>/aleksivanchenko/weds.json
        dest_path: /data/www-weds/
        domain: weds.by
        dir: /home/<USER>/aleksivanchenko
        source_path: /data/shared/weds.by
      tags:
        - aleksivanchenko
        - weds.by
